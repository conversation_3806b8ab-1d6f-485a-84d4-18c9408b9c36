/** @odoo-module **/
import { registry } from "@web/core/registry";
import { BlockUI } from "@web/core/ui/block_ui";
import { download } from "@web/core/network/download";

/**
 * XLSX Handler for Sale Report Advanced
 * This handler is responsible for generating XLSX reports for the sale_report_advanced module.
 * It sends a request to the server to generate the report in XLSX format and downloads the generated file.
 * @param {Object} action - The action object containing the report details.
 * @returns {Promise} - A promise that resolves when the report generation is complete.
 */
registry.category("ir.actions.report handlers").add("sale_report_advanced_xlsx", async function (action) {
    if (action.report_type === 'sale_report_advanced_xlsx') {
        const blockUI = new BlockUI();
        try {
            blockUI.block();
            await download({
                url: '/xlsx_reports',
                data: action.data,
            });
        } catch (error) {
            console.error('Error generating XLSX report:', error);
            throw error;
        } finally {
            blockUI.unblock();
        }
    }
});
