<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--    Template for invoice analysis report -->
    <template id="invoice_analysis_view">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t class="page">
                    <div class="oe_structure"/>
                    <center>
                        <b>
                            <h3>Invoice Analysis Report</h3>
                        </b>
                    </center>
                    <t t-if="start_date and end_date">
                        <center>
                            <span t-esc="start_date"/>
                            To
                            <span t-esc="end_date"/>
                        </center>
                    </t>
                    <t t-foreach="partner_id" t-as="partner">
                        <center>
                            <b>
                                <span t-esc="partner['name']"/>
                            </b>
                        </center>
                        <br/>
                        <t t-set="t_invoiced" t-value="0"/>
                        <t t-set="t_paid" t-value="0"/>
                        <t t-set="t_due" t-value="0"/>
                        <table class="table table-condensed">
                            <thead>
                                <tr>
                                    <th>Order Number</th>
                                    <th>Order Date</th>
                                    <th>Invoice Number</th>
                                    <th>Invoice Date</th>
                                    <th>Amount Invoiced</th>
                                    <th>Amount Paid</th>
                                    <th>Amount Due</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr t-foreach="form" t-as="order">
                                    <t t-if="order['partner_id'] == partner['id']">
                                        <td>
                                            <span t-esc="order['so']"/>
                                        </td>
                                        <td>
                                            <span t-esc="order['order_date']"
                                                  t-options='{"widget": "date"}'/>
                                        </td>
                                        <td>
                                            <span t-esc="order['invoice']"/>
                                        </td>
                                        <td>
                                            <span t-esc="order['date']"/>
                                        </td>
                                        <td>
                                            <span t-esc="order['invoiced']"/>
                                            <t t-set="t_invoiced"
                                               t-value="t_invoiced + order['invoiced']"></t>
                                        </td>
                                        <td>
                                            <span t-esc="order['paid']"/>
                                            <t t-set="t_paid"
                                               t-value="t_paid + order['paid']"></t>
                                        </td>
                                        <td>
                                            <span t-esc="order['due']"/>
                                            <t t-set="t_due"
                                               t-value="t_due + order['due']"></t>
                                        </td>
                                    </t>
                                </tr>
                                <tr>
                                    <td>
                                        <span>Total invoiced</span>
                                        <t t-esc="t_invoiced"/>
                                    </td>
                                    <td>
                                        <span>Total paid:</span>
                                        <t t-esc="t_paid"/>
                                    </td>
                                    <td>
                                        <span>Total due:</span>
                                        <t t-esc="t_due"/>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </t>
                </t>
            </t>
        </t>
    </template>
</odoo>
